# QuestionListingView Component

A comprehensive React component for displaying educational questions with support for multiple question types, responsive design, and question reordering functionality.

## Features

- 📱 **Mobile-Responsive**: Optimized for both desktop and mobile devices
- 🔄 **Question Reordering**: Built-in up/down buttons for reordering questions
- 📝 **Multiple Question Types**: Support for multiple choice, single choice, fill-in-the-blank, and creative writing
- 🎨 **Rich Content**: Support for HTML content, images, and SVG illustrations
- ♿ **Accessible**: Screen reader friendly with proper ARIA labels
- 📊 **Worksheet Information**: Sticky header with worksheet metadata

## Props

```typescript
export type QuestionListingViewProps = {
  questions?: Question[];
  containerClass?: string;
  isHtmlContent?: boolean;
  worksheetInfo?: {
    topic?: string;
    subject?: string;
    grade?: string;
    language?: string;
    level?: string;
    totalQuestions?: number;
  };
  // Reordering functionality
  allowReordering?: boolean;
  onReorderQuestion?: (fromIndex: number, toIndex: number) => void;
  isReordering?: boolean;
};
```

## Basic Usage

```tsx
import QuestionListingView from '@/components/molecules/QuestionListingView/QuestionListingView';

const MyComponent = () => {
  const questions = [
    {
      type: 'multiple_choice',
      content: 'What is the capital of France?',
      options: ['London', 'Berlin', 'Paris', 'Madrid'],
      answer: ['Paris'],
      explain: 'Paris is the capital and largest city of France.',
    },
    // ... more questions
  ];

  return (
    <QuestionListingView
      questions={questions}
      isHtmlContent={false}
    />
  );
};
```

## Usage with Reordering

```tsx
import { useState } from 'react';
import QuestionListingView from '@/components/molecules/QuestionListingView/QuestionListingView';

const EditableWorksheet = () => {
  const [questions, setQuestions] = useState(initialQuestions);
  const [isReordering, setIsReordering] = useState(false);

  const handleReorderQuestion = async (fromIndex: number, toIndex: number) => {
    setIsReordering(true);
    
    try {
      // Optimistic update
      const newQuestions = [...questions];
      const [movedQuestion] = newQuestions.splice(fromIndex, 1);
      newQuestions.splice(toIndex, 0, movedQuestion);
      setQuestions(newQuestions);

      // Call API to persist the change
      await reorderWorksheetQuestions(worksheetId, {
        questionIds: newQuestions.map(q => q.id)
      });
    } catch (error) {
      // Revert on error
      setQuestions(questions);
      console.error('Failed to reorder questions:', error);
    } finally {
      setIsReordering(false);
    }
  };

  return (
    <QuestionListingView
      questions={questions}
      allowReordering={true}
      onReorderQuestion={handleReorderQuestion}
      isReordering={isReordering}
      worksheetInfo={{
        subject: 'Mathematics',
        grade: 'Grade 5',
        totalQuestions: questions.length
      }}
    />
  );
};
```

## Question Types

### Multiple Choice
Questions with multiple options where multiple answers can be selected.

### Single Choice
Questions with multiple options where only one answer can be selected.

### Fill in the Blank
Questions with blanks that need to be filled with correct answers.

### Creative Writing
Open-ended questions that require written responses.

## Reordering Features

- **Up/Down Buttons**: Small, accessible buttons next to each question number
- **Smart Disabling**: Up button disabled for first question, down button disabled for last question
- **Loading State**: Buttons disabled during reordering operations
- **Accessibility**: Proper ARIA labels and tooltips
- **Mobile Optimized**: Touch-friendly button sizes

## Styling

The component uses Tailwind CSS classes and follows the existing design system. Key styling features:

- Responsive typography and spacing
- Card-based layout with shadows
- Sticky worksheet information header
- Consistent color scheme with primary/secondary colors
- Mobile-first responsive design

## Accessibility

- Proper ARIA labels for reorder buttons
- Keyboard navigation support
- Screen reader friendly content
- High contrast colors for better visibility
- Touch-friendly button sizes (minimum 44px)

## Dependencies

- React 18+
- Tailwind CSS
- DaisyUI (for some UI components)
- Lucide React (for icons)
