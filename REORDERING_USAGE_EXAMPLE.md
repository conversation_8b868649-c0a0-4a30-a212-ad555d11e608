# QuestionListingView Reordering Feature Usage

## Updated Component Features

The QuestionListingView component now includes up/down buttons for reordering questions. Here's how to use it:

## New Props Added

```typescript
export type QuestionListingViewProps = {
  // ... existing props
  allowReordering?: boolean;                                    // Enable/disable reordering buttons
  onReorderQuestion?: (fromIndex: number, toIndex: number) => void; // Callback when user clicks reorder
  isReordering?: boolean;                                       // Loading state during reordering
};
```

## Basic Usage Example

```tsx
import { useState } from 'react';
import QuestionListingView from '@/components/molecules/QuestionListingView/QuestionListingView';

const EditableWorksheet = () => {
  const [questions, setQuestions] = useState(initialQuestions);
  const [isReordering, setIsReordering] = useState(false);

  const handleReorderQuestion = async (fromIndex: number, toIndex: number) => {
    setIsReordering(true);
    
    try {
      // Optimistic update - immediately reorder in UI
      const newQuestions = [...questions];
      const [movedQuestion] = newQuestions.splice(fromIndex, 1);
      newQuestions.splice(toIndex, 0, movedQuestion);
      setQuestions(newQuestions);

      // Call your API to persist the change
      await reorderWorksheetQuestions(worksheetId, {
        questionIds: newQuestions.map(q => q.id)
      });
    } catch (error) {
      // Revert on error
      setQuestions(questions);
      console.error('Failed to reorder questions:', error);
    } finally {
      setIsReordering(false);
    }
  };

  return (
    <QuestionListingView
      questions={questions}
      allowReordering={true}                    // Enable reordering
      onReorderQuestion={handleReorderQuestion} // Handle reorder events
      isReordering={isReordering}              // Show loading state
      worksheetInfo={{
        subject: 'Mathematics',
        grade: 'Grade 5',
        totalQuestions: questions.length
      }}
    />
  );
};
```

## UI Features

- **Small up/down arrow buttons** next to each question number
- **Smart button states**: Up disabled for first question, down disabled for last question
- **Loading state**: Buttons disabled during reordering operations
- **Accessibility**: Proper ARIA labels and tooltips
- **Mobile responsive**: Touch-friendly button sizes
- **Only shows when**: `allowReordering={true}` and more than 1 question

## Button Behavior

- **Up button**: Moves question up one position (swaps with previous question)
- **Down button**: Moves question down one position (swaps with next question)
- **Disabled states**: Automatically handled based on position and loading state
- **Tooltips**: Show helpful messages like "Already at top" or "Move question up"

The component is now ready to use with reordering functionality!
